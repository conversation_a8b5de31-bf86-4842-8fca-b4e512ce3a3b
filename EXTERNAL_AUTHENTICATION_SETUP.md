# Azure AD External Authentication Setup Guide

This guide explains how to configure Azure AD external authentication for non-WHO accounts in the Malaria Surveillance Tool.

## Problem Solved

This configuration fixes the "Request is blocked" error that occurs when external users attempt to login using non-WHO accounts. The error was caused by:

1. **Azure AD Tenant Restrictions**: Azure AD was configured for single-tenant only, blocking external accounts
2. **Missing Multi-Tenant Support**: No support for Azure AD B2B guest accounts or external organizations
3. **Poor Error Handling**: Generic error messages without debugging information

## Configuration Steps

### 1. Azure AD Multi-Tenant Support

The application now supports external Azure AD accounts by using the common endpoint when `SupportExternalAccounts` is enabled.

**Configuration in appsettings.json:**

```json
"AzureAD": {
  "ClientId": "your-client-id",
  "ClientSecret": "your-client-secret",
  "TenantId": "your-tenant-id",
  "BaseUrl": "https://your-domain.com",
  "SupportExternalAccounts": true
}
```

**When `SupportExternalAccounts` is true:**

- Authority: `https://login.microsoftonline.com/common/v2.0`
- Supports external accounts from any organization or personal Microsoft accounts

**When `SupportExternalAccounts` is false:**

- Authority: `https://login.windows.net/{your-tenant-id}`
- Only supports accounts from your specific WHO tenant

### 2. Supported Account Types

With `SupportExternalAccounts: true`, the following account types can authenticate:

1. **Microsoft Personal Accounts**

   - @outlook.com, @hotmail.com, @live.com
   - Personal Microsoft accounts

2. **Work/School Accounts**

   - Accounts from any Azure AD organization
   - Corporate Microsoft 365 accounts

3. **Azure AD B2B Guest Accounts**
   - External users invited to your WHO tenant
   - Guest accounts from partner organizations

### 3. Azure App Service Configuration

For production deployment, set this environment variable in Azure App Service:

```
AzureAD__SupportExternalAccounts=true
```

This enables the common endpoint for external Azure AD authentication.

## Security Considerations

### Content Security Policy Updates

The application now includes Azure AD authentication domains in CSP:

- `https://login.microsoftonline.com` (Azure AD common endpoint)

### Cookie Security

- SameSite policy set to `SameAsRequest` for compatibility
- Secure cookies in production
- HttpOnly cookies for security

## Error Handling Improvements

### Enhanced Debugging

The application now provides detailed error logging for authentication failures:

- Authentication scheme validation
- Provider availability checking
- Detailed error messages in logs

### User-Friendly Error Pages

- Custom error page at `/authenticationerror`
- Provider-specific error messages
- Troubleshooting guidance for users

## Testing External Authentication

### Development Testing

1. Set `SupportExternalAccounts: true` in `appsettings.json`
2. Run the application: `dotnet run`
3. Navigate to login page
4. Select "Non-WHO User" option
5. Test with external Microsoft accounts (personal, work, or school accounts)

### Production Testing

1. Set `AzureAD__SupportExternalAccounts=true` in Azure App Service
2. Test with external Microsoft accounts from different organizations
3. Test with personal Microsoft accounts (@outlook.com, @hotmail.com, etc.)
4. Verify error handling and logging

## Troubleshooting

### Common Issues

1. **"Authentication scheme not configured"**

   - Ensure provider credentials are set in configuration
   - Check environment variables in production

2. **"Invalid redirect URI"**

   - Verify redirect URIs in provider configuration
   - Ensure BaseUrl matches your domain

3. **"External authentication error"**
   - Check application logs for detailed error information
   - Verify provider app/client configuration

### Debug Logging

Enable detailed logging by checking application logs for:

- `[EXTERNAL-AUTH-ERROR]` - Authentication failures
- `[EXTERNAL-AUTH-DEBUG]` - Authentication flow debugging
- `[AZURE-AD-CONFIG]` - Azure AD configuration details

## Security Best Practices

1. **Never commit secrets to source control**
2. **Use environment variables in production**
3. **Regularly rotate client secrets**
4. **Monitor authentication logs for suspicious activity**
5. **Keep redirect URIs minimal and specific**

## Support

For issues with external authentication:

1. Check application logs for detailed error messages
2. Verify provider configuration in respective consoles
3. Test with different external accounts
4. Contact system administrator if problems persist
