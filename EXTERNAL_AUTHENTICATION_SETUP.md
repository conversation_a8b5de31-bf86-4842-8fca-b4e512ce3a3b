# External Authentication Setup Guide

This guide explains how to configure external authentication providers (Google, Facebook, and Azure AD external accounts) for the Malaria Surveillance Tool.

## Problem Solved

This configuration fixes the "Request is blocked" error that occurs when external users attempt to login using non-WHO accounts. The error was caused by:

1. **Missing Authentication Providers**: Google and Facebook providers were not configured
2. **Azure AD Tenant Restrictions**: Azure AD was configured for single-tenant only
3. **Poor Error Handling**: Generic error messages without debugging information

## Configuration Steps

### 1. Azure AD Multi-Tenant Support

The application now supports external Azure AD accounts by using the common endpoint when `SupportExternalAccounts` is enabled.

**Configuration in appsettings.json:**
```json
"AzureAD": {
  "ClientId": "your-client-id",
  "ClientSecret": "your-client-secret", 
  "TenantId": "your-tenant-id",
  "BaseUrl": "https://your-domain.com",
  "SupportExternalAccounts": true
}
```

### 2. Google Authentication Setup

**Step 1: Create Google OAuth Application**
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing one
3. Enable Google+ API
4. Go to Credentials → Create Credentials → OAuth 2.0 Client ID
5. Set Application Type to "Web Application"
6. Add authorized redirect URIs:
   - `https://your-domain.com/signin-google`
   - `https://localhost:5001/signin-google` (for development)

**Step 2: Configure in appsettings.json:**
```json
"Google": {
  "ClientId": "your-google-client-id.apps.googleusercontent.com",
  "ClientSecret": "your-google-client-secret"
}
```

### 3. Facebook Authentication Setup

**Step 1: Create Facebook App**
1. Go to [Facebook Developers](https://developers.facebook.com/)
2. Create a new app
3. Add Facebook Login product
4. Configure Valid OAuth Redirect URIs:
   - `https://your-domain.com/signin-facebook`
   - `https://localhost:5001/signin-facebook` (for development)

**Step 2: Configure in appsettings.json:**
```json
"Facebook": {
  "AppId": "your-facebook-app-id",
  "AppSecret": "your-facebook-app-secret"
}
```

### 4. Azure App Service Configuration

For production deployment, set these environment variables in Azure App Service:

```
AzureAD__SupportExternalAccounts=true
Google__ClientId=your-google-client-id
Google__ClientSecret=your-google-client-secret
Facebook__AppId=your-facebook-app-id
Facebook__AppSecret=your-facebook-app-secret
```

## Security Considerations

### Content Security Policy Updates

The application now includes external authentication domains in CSP:
- `https://login.microsoftonline.com` (Azure AD)
- `https://accounts.google.com` (Google)
- `https://www.facebook.com` (Facebook)

### Cookie Security

- SameSite policy set to `SameAsRequest` for compatibility
- Secure cookies in production
- HttpOnly cookies for security

## Error Handling Improvements

### Enhanced Debugging

The application now provides detailed error logging for authentication failures:
- Authentication scheme validation
- Provider availability checking
- Detailed error messages in logs

### User-Friendly Error Pages

- Custom error page at `/authenticationerror`
- Provider-specific error messages
- Troubleshooting guidance for users

## Testing External Authentication

### Development Testing

1. Configure providers in `appsettings.json`
2. Run the application: `dotnet run`
3. Navigate to login page
4. Select "Non-WHO User" option
5. Test each provider (Google, Facebook, Azure AD external)

### Production Testing

1. Set environment variables in Azure App Service
2. Test with external accounts from different domains
3. Verify error handling for unconfigured providers

## Troubleshooting

### Common Issues

1. **"Authentication scheme not configured"**
   - Ensure provider credentials are set in configuration
   - Check environment variables in production

2. **"Invalid redirect URI"**
   - Verify redirect URIs in provider configuration
   - Ensure BaseUrl matches your domain

3. **"External authentication error"**
   - Check application logs for detailed error information
   - Verify provider app/client configuration

### Debug Logging

Enable detailed logging by checking application logs for:
- `[EXTERNAL-AUTH-ERROR]` - Authentication failures
- `[EXTERNAL-AUTH-DEBUG]` - Authentication flow debugging
- `[AZURE-AD-CONFIG]` - Azure AD configuration details

## Security Best Practices

1. **Never commit secrets to source control**
2. **Use environment variables in production**
3. **Regularly rotate client secrets**
4. **Monitor authentication logs for suspicious activity**
5. **Keep redirect URIs minimal and specific**

## Support

For issues with external authentication:
1. Check application logs for detailed error messages
2. Verify provider configuration in respective consoles
3. Test with different external accounts
4. Contact system administrator if problems persist
