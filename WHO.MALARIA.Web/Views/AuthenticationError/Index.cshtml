@{
    ViewData["Title"] = "Authentication Error";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header bg-danger text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-exclamation-triangle"></i>
                        Authentication Error
                    </h4>
                </div>
                <div class="card-body">
                    <div class="alert alert-danger" role="alert">
                        <strong>Authentication Failed</strong><br>
                        @ViewBag.ErrorMessage
                    </div>
                    
                    @if (!string.IsNullOrEmpty(ViewBag.Provider))
                    {
                        <p><strong>Provider:</strong> @ViewBag.Provider</p>
                    }
                    
                    @if (!string.IsNullOrEmpty(ViewBag.Error))
                    {
                        <p><strong>Error Type:</strong> @ViewBag.Error</p>
                    }

                    <div class="mt-4">
                        <h5>What you can do:</h5>
                        <ul>
                            <li>Try refreshing the page and attempting to login again</li>
                            <li>Clear your browser cache and cookies</li>
                            <li>Try using a different authentication method</li>
                            <li>Contact support if the problem persists</li>
                        </ul>
                    </div>

                    <div class="mt-4">
                        <a href="/" class="btn btn-primary">
                            <i class="fas fa-home"></i>
                            Return to Home
                        </a>
                        <a href="/idp" class="btn btn-secondary ml-2">
                            <i class="fas fa-sign-in-alt"></i>
                            Try Login Again
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
