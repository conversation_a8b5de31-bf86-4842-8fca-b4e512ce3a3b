using Microsoft.AspNetCore.Mvc;

namespace WHO.MALARIA.Web.Controllers
{
    /// <summary>
    /// Controller to handle authentication errors
    /// </summary>
    public class AuthenticationErrorController : Controller
    {
        /// <summary>
        /// Display authentication error page
        /// </summary>
        /// <param name="provider">The authentication provider that failed</param>
        /// <param name="error">The error type</param>
        /// <returns>Authentication error view</returns>
        public IActionResult Index(string provider = "", string error = "")
        {
            ViewBag.Provider = provider;
            ViewBag.Error = error;
            ViewBag.ErrorMessage = GetErrorMessage(provider, error);
            
            return View();
        }

        private string GetErrorMessage(string provider, string error)
        {
            if (error == "not_configured")
            {
                return $"Azure AD authentication is not currently configured. Please contact the administrator.";
            }

            if (error == "unsupported_provider")
            {
                return $"The authentication provider '{provider}' is not supported. Only Microsoft accounts (Azure AD) are supported for external authentication.";
            }

            if (error == "authentication_failed")
            {
                return "Microsoft authentication failed. This could be due to account restrictions, network issues, or configuration problems. Please try again or contact support.";
            }

            return $"Authentication failed. Please try again or contact support if the problem persists.";
        }
    }
}
