using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using System.Linq;
using System.Threading.Tasks;
using WHO.MALARIA.Domain.Models;

namespace WHO.MALARIA.Web.Controllers
{
    /// <summary>
    /// Controller for testing authentication configuration
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class AuthTestController : ControllerBase
    {
        private readonly AppSettings _appSettings;
        private readonly IAuthenticationSchemeProvider _schemeProvider;

        public AuthTestController(IOptions<AppSettings> appSettings, IAuthenticationSchemeProvider schemeProvider)
        {
            _appSettings = appSettings.Value;
            _schemeProvider = schemeProvider;
        }

        /// <summary>
        /// Test authentication configuration
        /// </summary>
        /// <returns>Authentication configuration status</returns>
        [HttpGet("config")]
        public async Task<IActionResult> GetAuthConfig()
        {
            var schemes = await _schemeProvider.GetAllSchemesAsync();
            
            var result = new
            {
                AzureAD = new
                {
                    Configured = !string.IsNullOrEmpty(_appSettings.AzureAD?.ClientId),
                    SupportExternalAccounts = _appSettings.AzureAD?.SupportExternalAccounts ?? false,
                    Authority = _appSettings.AzureAD?.SupportExternalAccounts == true
                        ? "https://login.microsoftonline.com/common/v2.0"
                        : $"https://login.windows.net/{_appSettings.AzureAD?.TenantId}",
                    SchemeAvailable = schemes.Any(s => s.Name.Equals("aad", System.StringComparison.OrdinalIgnoreCase))
                },
                AvailableSchemes = schemes.Select(s => new { s.Name, s.DisplayName }).ToList(),
                BaseUrl = _appSettings.AzureAD?.BaseUrl,
                ExternalAuthenticationNote = "Only Azure AD (Microsoft accounts) are supported for external authentication"
            };

            return Ok(result);
        }

        /// <summary>
        /// Test authentication scheme availability
        /// </summary>
        /// <param name="scheme">Authentication scheme to test</param>
        /// <returns>Scheme availability status</returns>
        [HttpGet("scheme/{scheme}")]
        public async Task<IActionResult> TestScheme(string scheme)
        {
            var schemes = await _schemeProvider.GetAllSchemesAsync();
            var targetScheme = schemes.FirstOrDefault(s => s.Name.Equals(scheme, System.StringComparison.OrdinalIgnoreCase));
            
            if (targetScheme == null)
            {
                return NotFound(new
                {
                    Error = $"Authentication scheme '{scheme}' not found",
                    AvailableSchemes = schemes.Select(s => s.Name).ToList()
                });
            }

            return Ok(new
            {
                Name = targetScheme.Name,
                DisplayName = targetScheme.DisplayName,
                HandlerType = targetScheme.HandlerType?.Name,
                Available = true
            });
        }
    }
}
